import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaPlus,
  FaTrash,
  FaEdit,
  FaKey,
  FaArrowLeft,
  FaSync,
  FaUserShield,
  FaUserCog,
  FaSearch,
  FaCheck,
  FaTimes,
  FaExclamationTriangle,
  FaUsers,
  FaUser,
  FaFilter,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';
import api from '../lib/axios';
import { useAuthStore } from '../stores/authStore';
import { useTheme } from '../contexts/ThemeContext';
import SelectBox from '../components/SelectBox';
import ToggleSwitch from '../components/ToggleSwitch';

interface User {
  id: number;
  username: string;
  full_name: string;
  email: string | null;
  role: 'admin' | 'cashier';
  is_active: boolean;
  created_at: string;
}

interface UserFormData {
  username: string;
  full_name: string;
  email: string;
  password: string;
  confirm_password: string;
  role: 'admin' | 'cashier';
  is_active: boolean;
}

const initialFormData: UserFormData = {
  username: '',
  full_name: '',
  email: '',
  password: '',
  confirm_password: '',
  role: 'cashier',
  is_active: true
};

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [formData, setFormData] = useState<UserFormData>(initialFormData);
  const [showPassword, setShowPassword] = useState(false);
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [showInactiveOnly, setShowInactiveOnly] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');

  // Pagination states
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [displayedUsers, setDisplayedUsers] = useState<User[]>([]);

  // Stats state
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    cashierUsers: 0
  });

  const { user: currentUser } = useAuthStore();
  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const navigate = useNavigate();

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();

    // Check if user has admin role
    if (currentUser?.role !== 'admin') {
      setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
    }
  }, [currentUser]);

  // Update stats when users change
  useEffect(() => {
    fetchStats();
  }, [users]);

  // Filter users when search term or filters change
  useEffect(() => {
    applyFilters();
  }, [searchTerm, users, showActiveOnly, showInactiveOnly, selectedRole]);

  // Update displayed users when filtered users or pagination changes
  useEffect(() => {
    updateDisplayedUsers();
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Calculate stats from users data
  const fetchStats = () => {
    setStats({
      totalUsers: users.length,
      activeUsers: users.filter(u => u.is_active).length,
      adminUsers: users.filter(u => u.role === 'admin').length,
      cashierUsers: users.filter(u => u.role === 'cashier').length
    });
  };

  // Apply filters to users
  const applyFilters = () => {
    let filtered = [...users];

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filters
    if (showActiveOnly && !showInactiveOnly) {
      filtered = filtered.filter(user => user.is_active);
    } else if (showInactiveOnly && !showActiveOnly) {
      filtered = filtered.filter(user => !user.is_active);
    }

    // Apply role filter
    if (selectedRole) {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    setFilteredUsers(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Update displayed users based on pagination
  const updateDisplayedUsers = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const usersToDisplay = filteredUsers.slice(startIndex, endIndex);
    setDisplayedUsers(usersToDisplay);
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  // Handle search
  const handleSearch = () => {
    applyFilters();
  };

  // Fetch users from API
  const fetchUsers = async () => {
    // Check if user has admin role first
    if (currentUser?.role !== 'admin') {
      console.log('User is not admin:', currentUser?.role);
      setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('Fetching users...');
      console.log('Current auth token:', useAuthStore.getState().token);

      // Add a timestamp to prevent caching
      const response = await api.get('/api/users/', {
        params: { _t: new Date().getTime() }
      });
      console.log('Users fetched successfully:', response.data);
      setUsers(response.data);
      setFilteredUsers(response.data);
      setIsLoading(false);
    } catch (error: any) {
      console.error('Error fetching users:', error);

      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data);
        if (error.response.status === 401) {
          console.log('Authentication error - token may be invalid');
          setErrorMessage('خطأ في المصادقة. يرجى تسجيل الخروج وإعادة تسجيل الدخول.');
        } else if (error.response.status === 403) {
          console.log('Authorization error - user may not have admin role');
          setErrorMessage('لا تملك صلاحيات كافية للوصول إلى هذه الصفحة');
        } else {
          console.log('Other API error:', error.response.status);
          setErrorMessage(`فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى. (${error.response.status})`);
        }
      } else if (error.request) {
        console.error('Error request - no response received:', error.request);
        setErrorMessage('فشل في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
      } else {
        console.error('Error setting up request:', error.message);
        setErrorMessage('فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى.');
      }

      setIsLoading(false);
    }
  };

  // Handle input change in form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  // Validate form before submit
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'الاسم الكامل مطلوب';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صالح';
    }

    if (!isEditMode) {
      if (!formData.password) {
        newErrors.password = 'كلمة المرور مطلوبة';
      } else if (formData.password.length < 6) {
        newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (formData.password !== formData.confirm_password) {
        newErrors.confirm_password = 'كلمات المرور غير متطابقة';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate password reset form
  const validatePasswordReset = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newPassword) {
      newErrors.newPassword = 'كلمة المرور الجديدة مطلوبة';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    if (newPassword !== confirmNewPassword) {
      newErrors.confirmNewPassword = 'كلمات المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Reset form to initial state
  const resetForm = () => {
    setFormData(initialFormData);
    setErrors({});
    setIsEditMode(false);
    setSelectedUserId(null);
    setShowForm(false);
  };

  // Open modal to create new user
  const handleNewUser = () => {
    resetForm();
    setShowForm(true);
  };

  // Open modal to edit user
  const handleEditUser = (user: User) => {
    setIsEditMode(true);
    setSelectedUserId(user.id);
    setFormData({
      username: user.username,
      full_name: user.full_name,
      email: user.email || '',
      password: '',
      confirm_password: '',
      role: user.role,
      is_active: user.is_active
    });
    setShowForm(true);
  };

  // Open modal to reset user password
  const handleResetPassword = (userId: number) => {
    setSelectedUserId(userId);
    setNewPassword('');
    setConfirmNewPassword('');
    setShowResetPassword(true);
  };

  // Format date with western numerals in DD/MM/YYYY format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  // Delete user
  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      return;
    }

    try {
      await api.delete(`/api/users/${userId}`);

      // Update users list
      setUsers(users.filter(user => user.id !== userId));
      setSuccessMessage('تم حذف المستخدم بنجاح');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error deleting user:', error);
      setErrorMessage('فشل في حذف المستخدم. يرجى المحاولة مرة أخرى.');

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Submit user form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditMode && selectedUserId) {
        // Update user
        const updateData = {
          username: formData.username,
          full_name: formData.full_name,
          email: formData.email || null,
          role: formData.role,
          is_active: formData.is_active
        };

        // Usar POST en lugar de PUT ya que el backend no tiene un endpoint PUT para /api/users/{id}
        await api.post(`/api/users/${selectedUserId}/update`, updateData);
        setSuccessMessage('تم تحديث المستخدم بنجاح');
      } else {
        // Create new user
        const userData = {
          username: formData.username,
          full_name: formData.full_name,
          email: formData.email || null,
          password: formData.password,
          role: formData.role,
          is_active: formData.is_active
        };

        await api.post('/api/users', userData);
        setSuccessMessage('تم إنشاء المستخدم بنجاح');
      }

      // Refresh users list
      fetchUsers();
      resetForm();

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error: any) {
      console.error('Error saving user:', error);

      if (error.response && error.response.data && error.response.data.detail) {
        if (error.response.data.detail.includes('already exists')) {
          setErrors({ ...errors, username: 'اسم المستخدم موجود بالفعل' });
        } else {
          setErrorMessage(`فشل في حفظ المستخدم: ${error.response.data.detail}`);
        }
      } else {
        setErrorMessage('فشل في حفظ المستخدم. يرجى المحاولة مرة أخرى.');
      }

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Submit password reset
  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePasswordReset()) {
      return;
    }

    try {
      // Usar POST en lugar de PUT para mantener consistencia con el endpoint de actualización
      await api.post(`/api/users/${selectedUserId}/reset-password`, {
        password: newPassword
      });

      setShowResetPassword(false);
      setSuccessMessage('تم تغيير كلمة المرور بنجاح');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error resetting password:', error);
      setErrorMessage('فشل في تغيير كلمة المرور. يرجى المحاولة مرة أخرى.');

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Check if user has admin role
  if (currentUser?.role !== 'admin') {
    return (
      <div className="touch-container">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/')}
              className="btn-icon mr-3"
            >
              <FaArrowLeft />
            </button>
            <h1 className="text-2xl font-bold text-secondary-900">إدارة المستخدمين</h1>
          </div>
        </div>

        {/* Error Message */}
        <div className="bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaExclamationTriangle className="ml-2" />
          لا تملك صلاحيات كافية للوصول إلى هذه الصفحة. يرجى تسجيل الدخول كمدير.
        </div>
      </div>
    );
  }

  return (
    <div className="touch-container">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaUsers className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المستخدمين</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة حسابات المستخدمين والصلاحيات
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={() => {
                  fetchUsers();
                  fetchStats();
                }}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="تحديث"
              >
                <FaSync className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={handleNewUser}
                className="bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium"
              >
                <FaPlus className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">إضافة مستخدم</span>
                <span className="sm:hidden lg:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      {successMessage && (
        <div className="bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaCheck className="ml-2" />
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center">
          <FaExclamationTriangle className="ml-2" />
          {errorMessage}
        </div>
      )}

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Users */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUsers className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المستخدمين</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{stats.totalUsers}</div>
            </div>
          </div>

          {/* Active Users */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUser className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">المستخدمين النشطين</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">{stats.activeUsers}</div>
            </div>
          </div>

          {/* Admin Users */}
          <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30">
            <div className="bg-purple-100 dark:bg-purple-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUserShield className="text-purple-600 dark:text-purple-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">المديرين</div>
              <div className="text-xl font-bold text-purple-600 dark:text-purple-400">{stats.adminUsers}</div>
            </div>
          </div>

          {/* Cashier Users */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUserCog className="text-blue-600 dark:text-blue-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">الكاشيرين</div>
              <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{stats.cashierUsers}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={handleSearch}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectBox
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value) => handleItemsPerPageChange(parseInt(value, 10))}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                />
              </div>

              {/* Role Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectBox
                  label="الدور"
                  name="role"
                  value={selectedRole}
                  onChange={(value) => setSelectedRole(value)}
                  options={[
                    { value: '', label: 'جميع الأدوار' },
                    { value: 'admin', label: 'مدير' },
                    { value: 'cashier', label: 'كاشير' }
                  ]}
                />
              </div>

              {/* Active Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المستخدمين النشطين
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="activeOnly"
                    checked={showActiveOnly}
                    onChange={(checked) => {
                      setShowActiveOnly(checked);
                      if (checked) setShowInactiveOnly(false);
                    }}
                    label="النشطين فقط"
                  />
                </div>
              </div>

              {/* Inactive Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المستخدمين غير النشطين
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="inactiveOnly"
                    checked={showInactiveOnly}
                    onChange={(checked) => {
                      setShowInactiveOnly(checked);
                      if (checked) setShowActiveOnly(false);
                    }}
                    label="غير النشطين فقط"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="sm:col-span-1 lg:col-span-1 flex flex-col gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm('');
                    setShowActiveOnly(false);
                    setShowInactiveOnly(false);
                    setSelectedRole('');
                    setItemsPerPage(10);
                    setCurrentPage(1);
                  }}
                  className="bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm"
                >
                  إعادة تعيين
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-14 w-14 border-b-3 border-primary-600 dark:border-primary-400"></div>
          </div>
        ) : displayedUsers.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    البريد الإلكتروني
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الدور
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {displayedUsers.map(user => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FaUser className="text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.full_name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            @{user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        {user.email || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.role === 'admin' ? (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                          <FaUserShield className="ml-1" />
                          مدير
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                          <FaUserCog className="ml-1" />
                          كاشير
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.is_active
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {user.is_active ? (
                          <>
                            <FaCheck className="ml-1" />
                            نشط
                          </>
                        ) : (
                          <>
                            <FaTimes className="ml-1" />
                            غير نشط
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {formatDate(user.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                          title="تعديل"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleResetPassword(user.id)}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"
                          title="تغيير كلمة المرور"
                        >
                          <FaKey />
                        </button>
                        {currentUser?.id !== user.id && (
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                            title="حذف"
                          >
                            <FaTrash />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <FaUsers className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'لم يتم العثور على مستخدمين مطابقين' : 'لا يوجد مستخدمين'}
            </p>
          </div>
        )}

        {/* Pagination */}
        {filteredUsers.length > 0 && (
          <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    عرض{' '}
                    <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>
                    {' '}إلى{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, filteredUsers.length)}
                    </span>
                    {' '}من{' '}
                    <span className="font-medium">{filteredUsers.length}</span>
                    {' '}نتيجة
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">السابق</span>
                      <FaChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                      if (
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 1 && page <= currentPage + 1)
                      ) {
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? 'z-10 bg-primary-50 dark:bg-primary-900/30 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-300'
                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      } else if (
                        page === currentPage - 2 ||
                        page === currentPage + 2
                      ) {
                        return (
                          <span
                            key={page}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            ...
                          </span>
                        );
                      }
                      return null;
                    })}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">التالي</span>
                      <FaChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* User Form Modal */}
      {showForm && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 w-full max-w-md mx-4 animate-fadeIn">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-medium text-secondary-900 dark:text-secondary-100">
                {isEditMode ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
              </h3>
              <button
                onClick={resetForm}
                className="text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl font-light"
              >
                &times;
              </button>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  اسم المستخدم*
                </label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 ${
                    errors.username
                      ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                      : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                  }`}
                />
                {errors.username && (
                  <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.username}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  الاسم الكامل*
                </label>
                <input
                  type="text"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 ${
                    errors.full_name
                      ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                      : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                  }`}
                />
                {errors.full_name && (
                  <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.full_name}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 ${
                    errors.email
                      ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                      : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                  }`}
                  dir="ltr"
                />
                {errors.email && (
                  <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.email}</p>
                )}
              </div>

              {!isEditMode && (
                <>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                      كلمة المرور*
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 pr-20 ${
                          errors.password
                            ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                            : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                        }`}
                        dir="ltr"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 px-4 flex items-center text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300"
                      >
                        {showPassword ? 'إخفاء' : 'عرض'}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.password}</p>
                    )}
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                      تأكيد كلمة المرور*
                    </label>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="confirm_password"
                      value={formData.confirm_password}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 ${
                        errors.confirm_password
                          ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                          : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                      }`}
                      dir="ltr"
                    />
                    {errors.confirm_password && (
                      <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.confirm_password}</p>
                    )}
                  </div>
                </>
              )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  الدور
                </label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 appearance-none"
                >
                  <option value="admin">مدير</option>
                  <option value="cashier">كاشير</option>
                </select>
              </div>

              <div className="mb-8">
                <label className="flex items-center cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`block w-14 h-8 rounded-full transition ${formData.is_active ? 'bg-success-500 dark:bg-success-600' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                    <div className={`absolute left-1 top-1 bg-white dark:bg-gray-200 w-6 h-6 rounded-full transition transform ${formData.is_active ? 'translate-x-6' : ''}`}></div>
                  </div>
                  <span className="mr-3 text-sm text-secondary-700 dark:text-secondary-300">المستخدم نشط</span>
                </label>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors"
                >
                  {isEditMode ? 'تحديث' : 'إضافة'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Reset Password Modal */}
      {showResetPassword && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 w-full max-w-md mx-4 animate-fadeIn">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-medium text-secondary-900 dark:text-secondary-100">تغيير كلمة المرور</h3>
              <button
                onClick={() => setShowResetPassword(false)}
                className="text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl font-light"
              >
                &times;
              </button>
            </div>

            <form onSubmit={handlePasswordReset}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  كلمة المرور الجديدة*
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 pr-20 ${
                      errors.newPassword
                        ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                        : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                    }`}
                    dir="ltr"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 px-4 flex items-center text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300"
                  >
                    {showPassword ? 'إخفاء' : 'عرض'}
                  </button>
                </div>
                {errors.newPassword && (
                  <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.newPassword}</p>
                )}
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1.5">
                  تأكيد كلمة المرور الجديدة*
                </label>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={confirmNewPassword}
                  onChange={(e) => setConfirmNewPassword(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 ${
                    errors.confirmNewPassword
                      ? 'border-danger-500 dark:border-danger-400 focus:ring-danger-500 dark:focus:ring-danger-400 focus:border-danger-500 dark:focus:border-danger-400'
                      : 'focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400'
                  }`}
                  dir="ltr"
                />
                {errors.confirmNewPassword && (
                  <p className="mt-1.5 text-sm text-danger-600 dark:text-danger-400">{errors.confirmNewPassword}</p>
                )}
              </div>

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowResetPassword(false)}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors"
                >
                  تغيير كلمة المرور
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users;